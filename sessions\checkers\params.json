{"self_play": {"mcts": {"gamma": 1, "cpuct": 1, "num_iters_per_turn": 200, "temperature": {"value": 1}, "dirichlet_noise_ϵ": 0.25, "dirichlet_noise_α": 1, "prior_temperature": 1}, "sim": {"num_games": 100, "num_workers": 32, "batch_size": 32, "use_gpu": false, "fill_batches": true, "reset_every": 4, "flip_probability": 0, "alternate_colors": false}}, "memory_analysis": {"num_game_stages": 4}, "learning": {"use_gpu": false, "use_position_averaging": true, "samples_weighing_policy": "LOG_WEIGHT", "optimiser": {"lr_base": 0.001, "lr_high": 0.01, "lr_low": 0.001, "momentum_low": 0.8, "momentum_high": 0.9}, "l2_regularization": 0.0001, "rewards_renormalization": 1, "nonvalidity_penalty": 1, "batch_size": 32, "loss_computation_batch_size": 512, "min_checkpoints_per_epoch": 0, "max_batches_per_checkpoint": 2000, "num_checkpoints": 1}, "arena": {"mcts": {"gamma": 1, "cpuct": 1, "num_iters_per_turn": 200, "temperature": {"value": 0.3}, "dirichlet_noise_ϵ": 0.1, "dirichlet_noise_α": 1, "prior_temperature": 1}, "sim": {"num_games": 20, "num_workers": 20, "batch_size": 20, "use_gpu": false, "fill_batches": true, "reset_every": 1, "flip_probability": 0.5, "alternate_colors": true}, "update_threshold": 0}, "num_iters": 30, "use_symmetries": false, "ternary_outcome": true, "mem_buffer_size": {"xs": [0], "ys": [20000]}}