# Tic-Tac-Toe

Default support for the game of Tic-Tac-Toe.

It is currently mainly used for CI testing and little tuning effort went into the training configuration file.

Also, note that Tic-Tac-Toe is not a very good example for illustrating and
understanding AlphaZero as its state space is too small. The game could be
played perfectly using MCTS alone as all reachable game states quickly end up
being part of the search tree.