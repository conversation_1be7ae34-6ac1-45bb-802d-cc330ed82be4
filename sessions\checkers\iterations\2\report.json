{"perfs_self_play": {"time": 739.4124893, "allocated": 1121925699432, "gc_time": 245.0929417}, "perfs_memory_analysis": {"time": 3.9017779, "allocated": 6127494992, "gc_time": 0.7909539}, "perfs_learning": {"time": 136.7738106, "allocated": 266331261040, "gc_time": 23.1738518}, "self_play": {"samples_gen_speed": 14.602579273931772, "average_exploration_depth": 8.403335103754113, "mcts_memory_footprint": 1728867870, "memory_size": 18286, "memory_num_distinct_boards": 17058}, "memory": {"latest_batch": {"num_samples": 10797, "num_boards": 10088, "Wtot": 10486.742, "status": {"loss": {"L": 2.2224374, "Lp": 0.54234856, "Lv": 0.6232073, "Lreg": 0.06175717, "Linv": 0.9947888}, "Hp": 0.8902104, "Hpnet": 1.4233682}}, "all_samples": {"num_samples": 18286, "num_boards": 17058, "Wtot": 17722.664, "status": {"loss": {"L": 1.8635842, "Lp": 0.36609703, "Lv": 0.44044265, "Lreg": 0.06175716, "Linv": 0.99496573}, "Hp": 1.010778, "Hpnet": 1.3709967}}, "per_game_stage": [{"min_remaining_length": 1, "max_remaining_length": 23, "samples_stats": {"num_samples": 4572, "num_boards": 4393, "Wtot": 4554.835, "status": {"loss": {"L": 1.4869548, "Lp": 0.27692127, "Lv": 0.15276618, "Lreg": 0.06175716, "Linv": 0.99531996}, "Hp": 0.9822984, "Hpnet": 1.2653301}}}, {"min_remaining_length": 23, "max_remaining_length": 47, "samples_stats": {"num_samples": 4572, "num_boards": 4419, "Wtot": 4539.3823, "status": {"loss": {"L": 1.6835386, "Lp": 0.2859705, "Lv": 0.34059227, "Lreg": 0.06175716, "Linv": 0.9949785}, "Hp": 1.1109501, "Hpnet": 1.3885443}}}, {"min_remaining_length": 47, "max_remaining_length": 80, "samples_stats": {"num_samples": 4572, "num_boards": 4367, "Wtot": 4476.5234, "status": {"loss": {"L": 1.9732372, "Lp": 0.4025125, "Lv": 0.5138131, "Lreg": 0.061757173, "Linv": 0.9948559}, "Hp": 1.0283267, "Hpnet": 1.4195254}}}, {"min_remaining_length": 80, "max_remaining_length": 254, "samples_stats": {"num_samples": 4570, "num_boards": 4108, "Wtot": 4298.6987, "status": {"loss": {"L": 2.3711061, "Lp": 0.5124898, "Lv": 0.8013025, "Lreg": 0.061757162, "Linv": 0.99468046}, "Hp": 0.9211296, "Hpnet": 1.423582}}}]}, "learning": {"time_convert": 0.5087352, "time_loss": 0.7632732, "time_train": 10.2140807, "time_eval": 124.0504782, "initial_status": {"loss": {"L": 1.8756934, "Lp": 0.36705837, "Lv": 0.4515462, "Lreg": 0.06175716, "Linv": 0.9949807}, "Hp": 1.010778, "Hpnet": 1.3693466}, "losses": [1.8788469, 1.7758876, 1.5640386, 1.920535, 1.7475039, 1.5489358, 1.8259906, 2.1870513, 1.53824, 1.3975064, 1.8539475, 1.8943363, 2.2974858, 1.9281406, 1.7180756, 1.9947501, 1.9977018, 2.3873885, 2.2447925, 1.6290327, 1.8886442, 1.9279735, 1.8515065, 1.4755722, 2.0718455, 2.0829027, 1.9222138, 1.7827363, 1.7877665, 1.6385549, 1.8754188, 1.9745996, 1.802773, 1.8873997, 2.1220245, 1.7314938, 1.9279944, 1.9088061, 2.1861567, 1.8635418, 2.2647586, 1.6886162, 1.8387285, 1.6498595, 1.9014964, 1.7480793, 1.968992, 1.7601899, 1.7202579, 1.392511, 1.8748373, 1.8389776, 2.0238373, 1.1871531, 1.9564385, 2.0851948, 1.8238741, 2.3457875, 1.9542179, 2.0468247, 2.0743752, 1.6994122, 1.5760956, 1.7705141, 1.6833681, 2.196789, 1.898051, 2.2292693, 1.8750732, 1.8757895, 1.78067, 1.652195, 1.9813248, 2.0540106, 1.6751856, 2.222363, 2.1754785, 1.5533222, 1.8020406, 1.9493027, 1.8309283, 1.8004954, 2.1806226, 1.88269, 2.2218828, 1.7563777, 1.6880625, 1.9838047, 1.925626, 2.282539, 2.1091537, 1.931379, 1.840613, 1.8428923, 1.7108222, 2.064194, 1.7494724, 1.2905836, 1.7601255, 1.8031113, 1.7567178, 2.1554112, 2.1646283, 1.7624844, 1.8641597, 1.8766012, 1.9396819, 1.7442777, 1.4506644, 1.8071498, 1.4480207, 1.9988462, 2.2284353, 2.238733, 2.128327, 1.671719, 1.8800361, 1.8695884, 1.9302757, 2.065682, 1.8075981, 1.649015, 1.8062629, 2.2351727, 2.9682934, 1.7322428, 2.1231349, 1.7556261, 1.7471312, 1.826998, 2.0917273, 2.026647, 1.8683884, 1.4628971, 1.6857558, 1.7725393, 1.9193271, 1.6410437, 1.8079501, 2.005148, 2.1523912, 1.6521205, 1.8166182, 2.2332885, 1.8495861, 1.6936072, 1.8041744, 1.7713656, 2.1376498, 2.0305037, 1.8332567, 1.8958912, 1.571241, 1.8635406, 2.0120897, 1.6550237, 2.0022395, 1.749353, 2.009634, 1.9457902, 1.7724919, 1.555136, 1.5985768, 1.6384664, 1.868884, 1.6809784, 1.8881632, 1.7230854, 2.3014271, 1.9912785, 1.6971484, 2.1184871, 1.6179756, 1.6176766, 1.6790755, 1.911309, 2.102152, 1.9538262, 1.8519796, 2.0034215, 1.3984647, 1.76598, 1.7375596, 1.7332038, 1.7654784, 1.72885, 2.1112309, 1.8657737, 1.5278928, 1.5936004, 2.101226, 1.6409367, 1.9962889, 1.5226468, 2.09578, 2.1712203, 1.8158427, 1.9153904, 1.5658509, 1.600549, 1.7115582, 1.7608992, 1.9811419, 1.5763134, 1.8774754, 1.8109378, 2.0366998, 1.5379046, 1.9402031, 1.6558322, 1.8912039, 1.850675, 1.7918593, 2.0953887, 2.0445044, 2.1174576, 1.5942007, 1.764554, 1.8779795, 1.5885636, 1.9399482, 1.7201986, 1.515991, 1.9835176, 2.1490333, 1.8494123, 1.709118, 1.7027066, 1.8792108, 1.5544556, 1.744939, 1.3555499, 1.7214266, 1.8417861, 1.7545933, 1.8125932, 1.9917544, 1.9610639, 2.4015691, 1.7849905, 1.405184, 1.7622902, 1.7940603, 1.7534481, 2.1392093, 1.7652917, 1.7946218, 1.9193865, 1.820401, 1.9264832, 1.8141943, 1.8618723, 2.0981236, 1.7979574, 1.8506225, 1.918817, 1.7435944, 1.9447674, 2.0247777, 1.7681359, 2.1597264, 1.8846285, 1.9100419, 1.8950344, 1.7378802, 1.7212088, 1.6832372, 2.0697145, 2.1393301, 1.9525106, 2.3514614, 2.1601098, 1.7303196, 1.8810003, 1.788251, 1.9860698, 1.6319699, 1.5795412, 2.0667095, 1.8617767, 1.596783, 1.5868928, 2.0054598, 1.9440665, 1.722288, 1.967889, 2.0587745, 1.6307478, 1.6814858, 1.9042987, 1.737533, 1.6300997, 1.8331264, 2.0417466, 1.9411243, 1.7668064, 1.9118706, 1.6080941, 1.7434907, 2.1253648, 1.7192963, 1.8543218, 1.7381611, 2.2705781, 1.7283615, 1.8204697, 1.9482169, 1.7469447, 1.9761108, 1.7776659, 1.8891729, 1.9196553, 1.6562004, 1.9088136, 1.8451748, 1.7275417, 1.7881919, 2.0448678, 1.8758054, 1.9622496, 1.4671088, 1.9066582, 2.0592165, 1.6306279, 2.1972673, 1.9205214, 2.441918, 1.8932222, 2.0418842, 1.6932756, 2.008168, 2.3787904, 1.9631101, 1.8918533, 1.8021204, 1.7865367, 1.9730572, 1.8995416, 1.8723332, 1.7694012, 1.839479, 1.8772284, 1.3337768, 1.4479383, 1.7913903, 2.3214517, 2.0243294, 2.02142, 1.6662148, 1.7324243, 1.8303356, 1.9734652, 2.0977523, 2.0072227, 2.0489101, 1.9962847, 1.9495615, 1.6826884, 1.9429317, 2.0173995, 1.9321531, 1.9536718, 1.8798288, 1.9478756, 1.9296712, 1.9640481, 1.7581702, 2.20052, 1.9530325, 1.6489027, 1.6126364, 1.6550511, 1.8520632, 2.0305257, 1.8562157, 1.9748508, 2.2622826, 1.536369, 2.0305402, 2.23753, 2.0446951, 1.5947328, 1.955222, 1.4164683, 1.7783153, 1.6652304, 1.9501704, 2.0047204, 1.8983216, 1.5037218, 1.799678, 1.8519448, 2.0986865, 1.8683563, 1.7006162, 1.7958685, 2.109621, 1.7356282, 1.547902, 1.7550868, 2.0779867, 1.954574, 1.7817596, 2.1558094, 1.9915202, 1.3973379, 1.6974926, 1.7514243, 1.4971422, 1.7116818, 2.008939, 1.9927739, 2.1312752, 1.6979749, 2.2248187, 1.7737093, 1.7932622, 1.5751867, 1.9059489, 1.8181338, 1.4122044, 1.9313588, 1.9670022, 1.883555, 1.5963377, 2.0426118, 1.7497857, 1.8581884, 1.9044747, 1.7797387, 1.6450695, 1.7773296, 1.995606, 1.5768989, 1.7695898, 1.6580179, 2.0256674, 1.7848835, 1.8975327, 1.6183192, 1.8101724, 2.0033193, 1.8185993, 1.8841813, 1.7894475, 1.9268658, 2.051964, 2.2632315, 2.0955534, 1.6324323, 2.0318913, 1.6122359, 1.6194605, 1.8643711, 1.5905559, 1.8447587, 1.8593463, 1.6083245, 1.7196642, 2.4791162, 1.8802004, 1.977799, 1.6809609, 1.8568156, 1.8734425, 1.8029602, 1.8017075, 1.9975034, 1.878368, 1.9239112, 1.7850413, 1.6945918, 1.9494829, 1.8520544, 1.7112947, 2.1426208, 2.0541053, 1.8310835, 1.9088844, 1.8279179, 1.6693388, 1.9998654, 1.8219737, 1.8027983, 1.8310096, 1.8724611, 1.7903086, 2.0708168, 1.890837, 1.9313613, 1.7285793, 2.009348, 1.476375, 1.8045517, 2.024641, 1.8014989, 1.9853513, 1.9307618, 1.8359001, 1.7120095, 1.6345675, 2.1799414, 1.8764846, 1.8355132, 1.5983241, 1.6816629, 1.481307, 1.6235766, 1.7229456, 1.6416434, 1.6632383, 1.9935075, 1.8287705, 1.6468548, 1.9319648, 1.9940552, 1.8926675, 1.7301484, 2.031433, 1.8334683, 2.007131, 1.5844055, 2.0232463, 1.7170105, 1.9983226, 2.224011, 1.9815573, 1.7509537, 2.1611288, 2.189907, 2.4964275, 1.9797008, 1.8984357, 1.8924809, 1.79753, 1.6703126, 1.9719852, 1.5858926, 1.5313343, 1.8242376, 1.8832606, 1.5450913, 1.5053811, 1.7628367, 1.7149526, 2.115643, 1.9949492, 1.5280144, 1.9351332, 1.8549966, 2.4317365, 2.281752, 1.8405474, 1.7142029, 1.9848856, 1.9602288, 1.474978, 1.8300316, 1.9146367, 1.899748, 1.7000382, 1.6398356, 1.5778857, 1.6353346, 1.7353127, 1.6401085, 1.8229265, 2.1017535, 1.6745473, 1.9090149, 1.7306854, 1.9499354, 1.8008641, 1.9942577, 1.5757565, 1.7324836, 1.7602341, 1.7843068, 1.7382928, 1.8904036, 1.7901696, 1.5529494, 1.3856843, 1.8304603, 1.718759, 1.9362026, 1.2652475, 1.7962931, 1.8488343, 1.5742806, 1.9639175, 1.9046904, 1.8390608, 1.837128, 1.4487703, 1.5884123, 1.5822123, 1.673623, 2.0245318, 1.6754136, 1.9315854, 1.7805189, 1.9040751, 1.6788244, 1.4693192, 1.958917, 1.8559177, 1.5379688, 2.0483837, 1.9936024, 1.4656665, 1.8245327, 1.9033843, 1.9089237, 1.5998118, 2.0755212, 1.9330615, 2.291978, 1.6325486, 1.6305445, 2.006854, 1.8655965, 2.1829374, 1.985935, 1.8015383, 1.5840558, 1.8111787, 1.6780943, 1.6354519, 1.7138888, 1.3188161, 1.7548476, 1.9168755, 1.5704029, 1.7608525, 1.9618567, 1.6169877, 1.6450362, 1.5278219, 1.7521439, 1.6875473, 1.3604423, 1.9569101, 1.3856653, 1.8526379, 1.8413057, 2.0023654, 2.1328351, 1.7553496, 1.9517541, 1.6125528, 1.7638613, 2.0798998, 1.7204404, 1.5758287, 1.7565376, 2.0361404, 2.8730094, 1.7066025, 2.1988308, 1.7386986, 1.6454134, 1.7794522, 1.9581976, 2.0382707, 1.8098726, 1.4865634, 1.6300864, 1.9404422, 1.8752865, 1.6058244, 1.7473204, 1.8515625, 2.0254323, 1.5913918, 1.6716205, 2.0416725, 1.7852356, 1.6444447, 1.7990229, 1.822972, 1.9670861, 1.9087863, 1.747205, 1.8558973, 1.5111963, 1.5775908, 2.0037158, 1.6015264, 1.8182881, 1.7539036, 2.1023755, 1.8739141, 1.8559618, 1.5465987, 1.4279466, 1.6063625, 1.7523092, 1.5840476, 1.9077872, 1.6070442, 2.037357, 2.0670779, 1.9783635, 2.0935242, 1.6291736, 1.579055, 1.7014254, 1.7562431, 2.110411, 1.925868, 1.8717892, 1.8687813, 1.5221634, 1.7191093, 1.6580222, 1.5830469, 1.5419327, 1.6507576, 2.0685356, 1.8820857, 1.4490062, 1.762022, 2.1015048, 1.5718107, 1.9320849, 1.4757082, 2.0655599, 2.0562034, 1.6449498, 1.6417749, 1.5309038, 1.5054792, 1.743444, 1.6988152, 1.9883837, 1.6180607, 1.7851906, 1.7753624, 1.7469128, 1.4752457, 1.8541814, 1.6388105, 1.7751579, 1.7468364, 1.7646847, 1.9297006, 2.0000885, 1.6979136, 1.5935086, 1.6642618, 1.7293813, 1.6068625, 1.8098762, 1.7836733, 1.3700781, 2.0349298, 2.1264617, 1.7957011, 1.6376178, 1.6605157, 1.7597424, 1.3624355, 1.6125896, 1.3426982, 1.6838423, 1.7837231, 1.8124176, 1.6405447, 1.9546912, 1.917533, 2.1465855, 1.7070494, 1.3296468, 1.6522105, 1.6870266, 1.6947913, 2.0348122, 1.7379558, 1.7884334, 1.7665424, 1.6962196, 1.7898216, 1.7094808, 1.890007, 2.0253966, 1.6344595, 1.74005, 1.9753284, 1.6479721, 1.9078687, 2.0299556, 1.6190459, 1.926499, 1.8167368, 1.8799844, 1.843754, 1.6182251, 1.559859, 1.6157881, 1.9156678, 1.9898963, 1.8957244, 2.3462768, 2.2002523, 1.522302, 1.8420923, 1.7568477, 1.8682683, 1.5659928, 1.593425, 1.949385, 1.7578573, 1.6077406, 1.6165386, 2.0043194, 1.7732614, 1.5831221, 1.8946284, 1.9053025, 1.6442933, 1.6378102, 1.8636553, 1.7715183, 1.5687896, 1.7885481, 1.864007, 1.9200552, 1.7742155, 1.8180698, 1.5264561, 1.7273343, 1.9970851, 1.4788859, 1.7235057, 1.7709217, 2.1400146, 1.6768974, 1.8796414, 2.0040817, 1.6342305, 1.9728047, 1.6662763, 1.8196557, 1.8667505, 1.5869204, 1.8370862, 1.80066, 1.6328858, 1.8029033, 2.04039, 1.9416972, 1.8348207, 1.5583465, 1.8701031, 2.0388458, 1.5414596, 2.0263772, 2.0865164, 2.2931612, 1.6810144, 1.9396614, 1.6262237, 1.8734251, 2.2773051, 1.8982873, 1.9576303, 1.602903, 1.7317609, 1.8813787, 1.8522913, 1.8167231, 1.6222758, 1.8602533, 1.6896687, 1.4330947, 1.4541478, 1.7943227, 2.2229445, 1.8351063, 2.015608, 1.6260517, 1.6087224, 1.8219867, 1.99046, 1.9971774, 1.9591125, 2.1538029, 1.8717452, 1.9803473, 1.6850739, 1.8534013, 2.0626543, 1.7242811, 1.893846, 1.8469915, 1.8834337, 1.8642219, 1.9123996, 1.7098624, 1.9647498, 1.974935, 1.562279, 1.5594723, 1.5432074, 1.9078267, 1.9625301, 1.8159835, 1.8450502, 2.207391, 1.4515362, 1.9929997, 2.250732, 2.0410135, 1.6022011, 1.850236, 1.4689832, 1.7134668, 1.6641645, 1.8923603, 1.8260069, 1.8637493, 1.4288969, 1.7572474, 1.9025524, 2.1378238, 1.7913231, 1.6955171, 1.7899976, 2.1779108, 1.9235623, 1.5690569, 1.6572185, 1.9138324, 1.8764527, 1.8660785, 2.010206, 1.8223718, 1.4258356, 1.5619639, 1.7778633, 1.4894223, 1.7601384, 1.8797892, 1.9084569, 2.1163483, 1.5836796, 2.0663743, 1.7267942, 1.7482257, 1.5284717, 1.8615503, 1.7922482, 1.451208, 1.8043371, 1.9637064, 1.8689487, 1.7514442, 2.0155108, 1.743701, 1.725899, 1.7715569, 1.7141758, 1.7229779, 1.7182378, 1.8172742, 1.6062591, 1.8961498, 1.5912232, 1.957607, 1.7356081, 1.8068163, 1.5619652, 1.7413045, 1.9029026, 1.7552006, 1.916815, 1.8047448, 1.7640437, 1.9204597, 2.3167179, 2.0226352, 1.6483011, 1.8867085, 1.5207694, 1.5989455, 1.9123274, 1.5222276, 1.8330661, 1.8129225, 1.6195489, 1.6232642, 2.4802558, 1.9375763, 1.9760728, 1.67708, 1.7601786, 1.7038689, 1.6581932, 1.7480806, 1.9228035, 1.8922129, 1.9378026, 1.7644546, 1.6450404, 1.8743055, 1.8519506, 1.7045866, 2.084846, 1.9824048, 1.6540097, 1.9017795, 1.8672097, 1.6033001, 1.9064627, 1.8757914, 1.7453754, 1.7776666, 1.8625529, 1.8170186, 1.9648123, 1.7396488, 1.8398938, 1.6747814, 1.9438457, 1.46635, 1.6997303, 1.9805765, 1.8107352, 1.9959828, 1.8394558, 1.8183131, 1.6890361, 1.5013101, 2.1287136, 1.7849419, 1.7282207, 1.5775156, 1.7036562, 1.4797286, 1.5520461, 1.5955484, 1.5921179, 1.6158147, 1.9318017, 1.6736703, 1.6650381, 1.8669579, 2.0074983, 1.8379346, 1.5561023, 2.004766, 1.8176861, 1.9483058, 1.5544233, 1.9833854, 1.6329045, 1.906338, 2.083806, 1.9557236, 1.736722, 2.197439, 2.0757954, 2.366675, 1.9805346, 1.7278168, 1.8142654, 1.6455877, 1.5980666, 1.9193773, 1.555145, 1.5202037, 1.793003, 1.730564, 1.4619111, 1.5064422, 1.7239835, 1.7375159, 2.0000675, 1.8509736, 1.4712306, 1.8551974, 1.8061424, 2.420355, 2.2134748, 1.7415142, 1.5971022, 1.8610293, 1.860292, 1.4798033, 1.8073819, 1.915393, 1.7850744, 1.6061683, 1.5449988, 1.5598779, 1.5639554, 1.7438247, 1.6137003, 1.7836622, 2.1310863, 1.6887312, 1.8731908, 1.6546899, 1.8678938, 1.7735459, 1.9205952, 1.5286219, 1.6827873, 1.7794697, 1.7485981, 1.6174413, 1.8959328, 1.7250234, 1.5193002, 1.4835677, 1.828954, 1.6596229, 1.8273042, 1.216596, 1.7207614, 1.7847155, 1.5552267, 1.9418888, 1.9126335, 1.8278551, 1.8010956, 1.4903367, 1.5989635, 1.482452, 1.5679073, 1.9321923, 1.5684278, 1.8336216, 1.7774274, 1.8716244, 1.728109, 1.524838, 1.9039793, 1.815786, 1.5028244, 1.9523178, 1.9603895, 1.5282061, 1.7771307, 1.7977552, 1.9209499, 1.4788293, 1.9702116, 1.9412774, 2.119628, 1.6067096, 1.6763084, 1.8488084, 1.844324, 2.0933878, 1.9211361, 1.9121498, 1.5170549, 1.7318958, 1.5663842, 1.7667574, 1.7561823, 1.2566061, 1.757923, 1.818814, 1.5214537, 1.7082227, 1.9200966, 1.5678624, 1.7144331, 1.4765614, 1.7110293, 1.6986729, 1.3513123, 1.895529, 1.3684921, 1.7762903, 1.7948772, 1.8562388, 2.1368272, 1.5962775, 1.9235007, 1.5905242, 1.6830145, 1.9841939, 1.5829301, 1.5478504, 1.7854629, 2.0204775, 2.8379667, 1.6611387, 2.0404754, 1.8382353, 1.5158204, 1.7238616, 1.8382112, 1.8786786, 1.8002423, 1.4011259, 1.6069362, 1.8648643, 1.8384564, 1.5566726, 1.5996346, 1.8129182, 1.987512, 1.6084023, 1.6673986, 2.002603, 1.7426305, 1.5647446, 1.8248181, 1.6973242, 1.7750912, 1.8945718, 1.6801403, 1.8165394, 1.5025532, 1.4946797, 1.9367837, 1.6153059, 1.788715, 1.7450932, 1.920992, 1.8182019, 1.8420205, 1.5314459, 1.3802313, 1.47143, 1.6516101, 1.5401105, 1.8623786, 1.5674393, 2.1375527, 1.9653046, 1.7586108, 2.0803158, 1.597256, 1.5046232, 1.6575514, 1.7124302, 2.0865712, 1.8603959, 1.8441873, 1.7863604, 1.4331703, 1.6954577, 1.6337147, 1.5547631, 1.53322, 1.616662, 1.9485062, 1.7684273, 1.4258609, 1.7288263, 2.0105808, 1.5680795, 1.8712142, 1.4336337, 2.0315711, 1.9717498, 1.5856682, 1.6824956, 1.3987794, 1.4794619, 1.666625, 1.7019992, 1.8999616, 1.4936211, 1.6770022, 1.6731876, 1.7576306, 1.4774277, 1.7093832, 1.6475208, 1.7648596, 1.7364632, 1.6742023, 1.8278025, 1.8786567, 1.697138, 1.5733927, 1.6443826, 1.6459776, 1.5408678, 1.8072524, 1.7086539, 1.3978631, 1.8335814, 2.018701, 1.7820898, 1.598693, 1.6416595, 1.7287301, 1.326467, 1.5940056, 1.3328766, 1.6367276, 1.7552515, 1.7212621, 1.6487083, 1.8396193, 1.8434887, 2.0830822, 1.6862953, 1.3885572, 1.5114758, 1.6917267, 1.6133275, 1.9406698, 1.7949811, 1.7454842, 1.7931879, 1.684292, 1.6848344, 1.6125273, 1.8107201, 1.926076, 1.6688833, 1.74396, 1.8596758, 1.6727737, 1.7996395, 1.893766, 1.6251191, 1.8081278, 1.7672973, 1.9333739, 1.836638, 1.5564198, 1.5971177, 1.6315309, 1.8940964, 1.9496633, 1.8456067, 2.26106, 1.9327028, 1.4486753, 1.8286511, 1.689886, 1.7746235, 1.4936306, 1.5315652, 2.0304084, 1.7382761, 1.5107313, 1.5502971, 1.9803774, 1.7703179, 1.5746865, 1.8639294, 1.8889658, 1.560683, 1.5645231, 1.7103151, 1.7547293, 1.5419588, 1.7308686, 1.7825505, 1.8735005, 1.6412462, 1.7635299, 1.5309283, 1.7624607, 1.8721716, 1.4841557, 1.8226409, 1.667618, 2.0678747, 1.7208273, 1.7999873, 1.9163924, 1.5396335, 1.9521601, 1.6368194, 1.7739106, 1.8667475, 1.5601518, 1.7256333, 1.7627208, 1.5725318, 1.7961546, 1.8848956, 1.8836302, 1.761867, 1.5145832, 1.8019329, 2.005464, 1.5148233, 2.0495827, 1.9221613, 2.1996393, 1.6822045, 1.7179065, 1.5255355, 1.8340888, 2.1779222, 1.8480132, 1.880641, 1.5584065, 1.7617512, 1.6832595, 1.8534831, 1.8039594, 1.5351624, 1.7717738, 1.6256095, 1.3622997, 1.4297162, 1.7413511, 2.0653548, 1.8720824, 1.9640607, 1.6054137, 1.5618522, 1.8031275, 1.8851451, 2.0282457, 1.85682, 1.9133278, 1.8750587, 1.8283174, 1.6899191, 1.8641939, 1.9605331, 1.7831744, 1.8221736, 1.7717924, 1.8337613, 1.7649108, 1.895215, 1.6120151, 1.8239403, 1.7809063, 1.6427736, 1.5883222, 1.4818014, 1.8498375, 1.9030904, 1.7870628, 1.8596951, 2.130026, 1.4742516, 1.9777521, 2.1655362, 1.8168521, 1.5654644, 1.8227303, 1.3894855, 1.6188911, 1.6565765, 1.847117, 1.8802334, 1.8007241, 1.3760853, 1.7314031, 1.8544315, 2.0457227, 1.635452, 1.5975189, 1.6215935, 2.042648, 1.7426838, 1.5270208, 1.6298261, 1.8754092, 1.8310733, 1.7230198, 1.9997278, 1.7610471, 1.3142071, 1.4857252, 1.663443, 1.5416316, 1.6314757, 1.8566353, 1.7753394, 1.8735732, 1.5487658, 2.0057719, 1.6511897, 1.660338, 1.4331642, 1.7186291, 1.7395812, 1.4304136, 1.8257024, 1.9034295, 1.6708788, 1.6155015, 1.9295832, 1.628256, 1.611617, 1.7592598, 1.5951276, 1.6088991, 1.6425884, 1.7379352, 1.5157062, 1.7034471, 1.4831865, 1.8323401, 1.6832156, 1.7550808, 1.4879743, 1.6680555, 1.7527755, 1.7194345, 1.6971631, 1.7796197, 1.6830161, 1.9352237, 1.9974316, 1.8980173, 1.5536649, 1.9535099, 1.5526607, 1.5790038, 1.7779535, 1.4365937, 1.6679521, 1.8299968, 1.5974063, 1.6459564, 2.3464491, 1.7722787, 1.8393767, 1.5664544, 1.6220349, 1.7336892, 1.6125606, 1.6368945, 1.7805561, 1.8469929, 1.845474, 1.6305082, 1.5571686, 1.7934885, 1.6618924, 1.6421068, 2.023015, 1.8185834, 1.7241468, 1.727204, 1.7794882, 1.5954363, 1.838979, 1.800945, 1.6436408, 1.6798795, 1.808917, 1.7607716, 1.9327294, 1.6638093, 1.776739, 1.7472708, 1.8169008, 1.406639, 1.6604373, 1.8685026, 1.6922016, 1.8765961, 1.7783153, 1.7778757, 1.6195831, 1.4477031, 1.9879218, 1.819304, 1.7155995, 1.4419649, 1.5071299, 1.3962212, 1.5196782, 1.6040115, 1.5806046, 1.4975202, 1.8920509, 1.570316, 1.6390536, 1.8202813, 2.0138528, 1.765015, 1.5182234, 2.0404813, 1.7673444, 1.8490574, 1.5572673, 1.9612043, 1.6061412, 1.8834987, 2.085575, 1.9226815, 1.7138846, 2.0843232, 1.9682505, 2.0931447, 1.8973702, 1.685531, 1.8040122, 1.6082675, 1.4876342, 1.8971466, 1.4491377, 1.446051, 1.8028643, 1.6740632, 1.3466007, 1.5029287, 1.671952, 1.6321692, 1.9485679, 1.7145635, 1.4247625, 1.7941842, 1.7428145, 2.429812, 2.2173545, 1.6967674, 1.5283018, 1.8504715, 1.8569368, 1.4528364, 1.6976064, 1.8619181, 1.7015922, 1.5717405, 1.4613013, 1.505628, 1.4715827, 1.6957495, 1.5615425, 1.7415059, 2.083829, 1.6521788, 1.8809433, 1.5939301, 1.9121228, 1.8309894, 1.9256477, 1.5537837, 1.6836109, 1.5410177, 1.6133835, 1.5783825, 1.7570152, 1.6641353, 1.5244635, 1.3833468, 1.6743237, 1.5675553, 1.7740083, 1.2029341, 1.6460894, 1.7969165, 1.5313827, 1.8565178, 1.8865318, 1.7712263, 1.694011, 1.3908802, 1.4962231, 1.4349945, 1.5166949, 1.8105843, 1.4998, 1.7974094, 1.8098668, 1.8978828, 1.629896, 1.45412, 1.8373445, 1.702132, 1.486306, 1.874924, 2.0430732, 1.501084, 1.6627709, 1.8154817, 1.7654134, 1.6029615, 1.8380932, 1.792304, 2.01832, 1.5593231, 1.5748264, 1.7713304, 1.7083735, 2.04787, 1.8315748, 1.75808, 1.4294456, 1.6993245, 1.5722522, 1.6835848, 1.7183841, 1.2738373, 1.6560411, 1.7476616, 1.3689297, 1.604784, 1.7624751, 1.5796155, 1.6760468, 1.4434687, 1.6756018, 1.6236287, 1.2497038, 1.8062534, 1.345398, 1.7762543, 1.8931565, 1.817166, 2.0613441, 1.5035737, 1.666977, 1.5182456, 1.6904151, 1.8298236, 1.4828455, 1.5239334, 1.7020545, 1.9491044, 2.7343924, 1.6272389, 1.979879, 1.7880926, 1.4501718, 1.6839758, 1.8011073, 1.7119504, 1.7776384, 1.2782512, 1.6693553, 1.751548, 1.7718925, 1.4196069, 1.4646233, 1.7542778, 1.8800802, 1.5697753, 1.6922582, 1.9213543, 1.7395126, 1.5628623, 1.8126906, 1.7050998, 1.6623384, 1.7802529, 1.6904343, 1.7787114, 1.5001515, 1.383156, 1.8323907, 1.5407857, 1.7096701, 1.694301, 1.8040289, 1.8314049, 1.8316575, 1.550322, 1.3781838, 1.3977307, 1.5344803, 1.4740169, 1.9654156, 1.4368328, 2.0203912, 1.9137188, 1.5426801, 1.9754033, 1.5214936, 1.4098227, 1.5959746, 1.627995, 1.9922037, 1.7588398, 1.6696032, 1.581876, 1.4166789, 1.6305393, 1.6186827, 1.5740776, 1.4911971, 1.5576074, 1.8602864, 1.6954755, 1.3791438, 1.603958, 1.8862687, 1.5331998, 1.7874097, 1.4094024, 1.9036539, 1.9315734, 1.5483159, 1.5203654, 1.2987132, 1.3793505, 1.6083504, 1.6181762, 1.81158, 1.3790276, 1.6484257, 1.6384727, 1.6658, 1.4508904, 1.6123977, 1.6135476, 1.6475447, 1.6917264, 1.6313473, 1.7853171, 1.8261318, 1.6435276, 1.4848262, 1.6185837, 1.607985, 1.4743683, 1.8109837, 1.6608034, 1.3242029, 1.7363251, 1.9271376, 1.7094698, 1.4838326, 1.6101702, 1.5831344, 1.2385546, 1.5543222, 1.3011636, 1.5653228, 1.7129323, 1.5921363, 1.6575123, 1.7777883, 1.7488949, 1.9737111, 1.6613257, 1.3885038, 1.4102718, 1.5591826, 1.6029936, 1.8680375, 1.784475, 1.759964, 1.701172, 1.6224334, 1.5401434, 1.5891838, 1.7801255, 1.8411691, 1.72951, 1.70108, 1.7260606, 1.6192743, 1.7160991, 1.8031662, 1.5680352, 1.7621047, 1.7734684, 1.6049266, 1.7375646, 1.5671955, 1.5627459, 1.6282074, 1.9162726, 1.8827523, 1.7409424, 2.0712376, 1.9843711, 1.3802062, 1.7171129, 1.6870977, 1.6201146, 1.5211403, 1.5526924, 1.9934528, 1.7015816, 1.4668272, 1.5426723, 1.7176944, 1.6289976, 1.5018064, 1.7562522, 1.8877126, 1.4899156, 1.5624561, 1.7448671, 1.7193356, 1.4293069, 1.7139719, 1.5836574, 1.8380774, 1.6654471, 1.6779387, 1.5127269, 1.6530322, 1.8185899, 1.427572, 1.7577345, 1.6776115, 1.8829583, 1.6227746, 1.836163, 1.9362717, 1.5211391, 1.8573196, 1.5704398, 1.7415159, 1.8118247, 1.5042245, 1.7389809, 1.6770494, 1.5263358, 1.7954777, 1.7451704, 1.8141148, 1.6936592, 1.4951724, 1.6498051, 1.8036953, 1.424158, 1.8972149, 1.7142103, 1.9992789, 1.6572396, 1.6113385, 1.4863644, 1.8220736, 1.9453589, 1.8406719, 1.7898679, 1.566131, 1.6965891, 1.680924, 1.7545683, 1.7754157, 1.5155112, 1.7385516, 1.5512315, 1.3406515, 1.3463032, 1.6887236, 1.9616737, 1.7846104, 1.7666491, 1.6727644, 1.505419, 1.7205912, 1.8659574, 1.9892381, 1.7806796, 1.731615, 1.7758317, 1.8185973, 1.665423, 1.8928746, 1.8053257, 1.6174566, 1.6579974, 1.7081255, 1.7349885, 1.6535946, 1.8049356, 1.6061784, 1.7641966, 1.7474371, 1.637999, 1.5340947, 1.4389155, 1.8159473, 1.8370041, 1.8435938, 1.7281544, 2.0937104, 1.3653314, 1.9050927, 2.198399, 1.6937034, 1.5184958, 1.7308095, 1.3363445, 1.6036195, 1.5920814, 1.7141808, 1.7615784, 1.7837325, 1.3599249, 1.671648, 1.863349, 1.9819106, 1.5776445, 1.5133048, 1.5901794, 1.9453415, 1.7098562, 1.518621, 1.522532, 1.749297], "checkpoints": [{"batch_id": 2000, "evaluation": {"legend": "Most recent NN versus best NN so far", "avgr": 0.55, "redundancy": 0.10816696914700541, "rewards": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, -1, 0, 1, 1, -1, -1, 1, -1], "baseline_rewards": null, "time": 124.0504782}, "status_after": {"loss": {"L": 1.639033, "Lp": 0.33996677, "Lv": 0.24212167, "Lreg": 0.061812088, "Linv": 0.9948437}, "Hp": 1.010778, "Hpnet": 1.3581997}, "nn_replaced": true}], "nn_replaced": true}}