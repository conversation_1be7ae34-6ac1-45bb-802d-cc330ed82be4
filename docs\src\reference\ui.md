# [User Interface](@id ui)

```@meta
CurrentModule = AlphaZero
```

```@docs
UserInterface
```

```@meta
CurrentModule = AlphaZero.UserInterface
```

## [Session](@id session)

```@docs
Session
```

![Session CLI (first iteration)](../assets/img/ui-first-iter.png)

```@docs
Session(::Experiment) # Strangely, this includes all constructors...
resume!
save
SessionReport
```

## [Explorer](@id explorer)

![Explorer](../assets/img/explorer.png)

```@docs
explore
```